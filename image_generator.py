#!/usr/bin/env python3
"""
🖼️ Image Generator using Pollinations AI API
Generates sick images from text prompts, no bullshit
"""

import requests
import urllib.parse
import os
import sys
from datetime import datetime
import argparse
from dotenv import load_dotenv

# load environment variables
load_dotenv()

class ImageGenerator:
    def __init__(self):
        self.base_url = "https://image.pollinations.ai/prompt"
        self.token = os.getenv('POLLINATIONS_TOKEN')  # optional auth token
        self.default_params = {
            "model": "flux",
            "width": 2500,
            "height": 1600,
            "safe": "false",
            "nologo": "true",
            "private": "true",
            "enhance": "false"
        }

        # setup headers for auth if token exists
        self.headers = {}
        if self.token:
            self.headers['Authorization'] = f'Bearer {self.token}'
            print(f"🔑 using auth token: {self.token[:10]}...")
        else:
            print("🔓 no auth token found, using public access")
    
    def generate_image(self, prompt, output_file=None, **kwargs):
        """
        Generate image from text prompt
        
        Args:
            prompt (str): Text description of the image
            output_file (str): Output filename (optional)
            **kwargs: Additional parameters (width, height, seed, etc.)
        
        Returns:
            str: Path to saved image file
        """
        # merge custom params with defaults
        params = {**self.default_params, **kwargs}
        
        # encode that prompt shit properly
        encoded_prompt = urllib.parse.quote(prompt)
        url = f"{self.base_url}/{encoded_prompt}"
        
        print(f"🎨 generating image for: '{prompt}'")
        print(f"🔗 url: {url}")
        print(f"⚙️ params: {params}")
        
        try:
            # make the damn request with auth headers
            response = requests.get(url, params=params, headers=self.headers, timeout=300)
            response.raise_for_status()
            
            # figure out output filename
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_prompt = "".join(c for c in prompt[:30] if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_prompt = safe_prompt.replace(' ', '_')
                output_file = f"generated_{safe_prompt}_{timestamp}.jpg"
            
            # save that beautiful image
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ image saved as: {output_file}")
            return output_file
            
        except requests.exceptions.RequestException as e:
            print(f"❌ error fetching image: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"response text: {e.response.text}")
            return None
    
    def generate_from_file(self, prompt_file="prompt.txt"):
        """
        Generate image from prompt in a text file
        
        Args:
            prompt_file (str): Path to file containing the prompt
        """
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            
            if not prompt:
                print(f"❌ empty prompt file: {prompt_file}")
                return None
                
            print(f"📖 reading prompt from: {prompt_file}")
            return self.generate_image(prompt)
            
        except FileNotFoundError:
            print(f"❌ prompt file not found: {prompt_file}")
            return None
        except Exception as e:
            print(f"❌ error reading prompt file: {e}")
            return None

def get_next_image_number(chapter_dir):
    """
    Get the next image number by reading count.txt or counting existing images

    Args:
        chapter_dir (str): Path to chapter directory

    Returns:
        int: Next image number to use
    """
    count_file = os.path.join(chapter_dir, "count.txt")

    # try to read from count.txt first
    if os.path.exists(count_file):
        try:
            with open(count_file, 'r') as f:
                count = int(f.read().strip())
                return count + 1
        except (ValueError, IOError):
            pass

    # fallback: count existing png files
    if os.path.exists(chapter_dir):
        png_files = [f for f in os.listdir(chapter_dir) if f.endswith('.png') and f[:-4].isdigit()]
        if png_files:
            numbers = [int(f[:-4]) for f in png_files]
            return max(numbers) + 1

    return 1

def update_image_count(chapter_dir, count):
    """
    Update the count.txt file with current image count

    Args:
        chapter_dir (str): Path to chapter directory
        count (int): Current image count
    """
    count_file = os.path.join(chapter_dir, "count.txt")
    try:
        with open(count_file, 'w') as f:
            f.write(str(count))
    except IOError as e:
        print(f"⚠️ warning: couldn't update count file: {e}")

def generate_story_image(generator, название, глава, prompt, negative_prompt="", **extra_params):
    """
    Generate image for story with proper folder structure

    Args:
        generator: ImageGenerator instance
        название (str): Story name (folder name)
        глава (str): Chapter name (subfolder name)
        prompt (str): Image generation prompt
        negative_prompt (str): Negative prompt for what to avoid
        **extra_params: Additional generation parameters (seed, width, etc.)

    Returns:
        str: Path to generated image
    """
    # create directory structure: название/глава/
    story_dir = os.path.join(название)
    chapter_dir = os.path.join(story_dir, глава)

    # make sure directories exist
    os.makedirs(chapter_dir, exist_ok=True)

    # get next image number
    image_num = get_next_image_number(chapter_dir)

    # create output filename
    output_file = os.path.join(chapter_dir, f"{image_num}.png")

    print(f"📁 story: {название}")
    print(f"📖 chapter: {глава}")
    print(f"🖼️ generating image #{image_num}")
    if extra_params:
        print(f"🔧 extra params: {extra_params}")

    # prepare generation params
    params = {}
    if negative_prompt:
        params["negative"] = negative_prompt

    # add any extra params (seed, width, etc.)
    params.update(extra_params)

    # generate the fucking image
    result = generator.generate_image(prompt, output_file, **params)

    if result:
        # update count file
        update_image_count(chapter_dir, image_num)
        print(f"✅ image saved: {result}")
        print(f"📊 updated count to: {image_num}")

    return result

def main():
    # check if we have the new 4+ argument format for stories (before argparse fucks it up)
    if len(sys.argv) >= 5 and not any(arg.startswith('-') for arg in sys.argv[1:5]):
        # new format: python image_generator.py "название" "глава" "prompt" "negative_prompt" [optional flags]
        название = sys.argv[1]
        глава = sys.argv[2]
        prompt = sys.argv[3]
        negative_prompt = sys.argv[4]

        # parse any additional flags after the 4 main args
        extra_params = {}
        remaining_args = sys.argv[5:]  # everything after the 4 main args

        i = 0
        while i < len(remaining_args):
            arg = remaining_args[i]
            if arg == '-s' or arg == '--seed':
                if i + 1 < len(remaining_args):
                    try:
                        extra_params['seed'] = int(remaining_args[i + 1])
                        i += 2
                    except ValueError:
                        print(f"⚠️ invalid seed value: {remaining_args[i + 1]}")
                        i += 1
                else:
                    print("⚠️ -s/--seed requires a value")
                    i += 1
            elif arg == '-w' or arg == '--width':
                if i + 1 < len(remaining_args):
                    try:
                        extra_params['width'] = int(remaining_args[i + 1])
                        i += 2
                    except ValueError:
                        print(f"⚠️ invalid width value: {remaining_args[i + 1]}")
                        i += 1
                else:
                    print("⚠️ -w/--width requires a value")
                    i += 1
            elif arg == '--height':
                if i + 1 < len(remaining_args):
                    try:
                        extra_params['height'] = int(remaining_args[i + 1])
                        i += 2
                    except ValueError:
                        print(f"⚠️ invalid height value: {remaining_args[i + 1]}")
                        i += 1
                else:
                    print("⚠️ --height requires a value")
                    i += 1
            elif arg == '-m' or arg == '--model':
                if i + 1 < len(remaining_args):
                    extra_params['model'] = remaining_args[i + 1]
                    i += 2
                else:
                    print("⚠️ -m/--model requires a value")
                    i += 1
            elif arg == '--transparent':
                extra_params['transparent'] = 'true'
                i += 1
            else:
                print(f"⚠️ unknown flag: {arg}")
                i += 1

        generator = ImageGenerator()
        generate_story_image(generator, название, глава, prompt, negative_prompt, **extra_params)
        return

    # fallback to old argument parsing for backwards compatibility
    parser = argparse.ArgumentParser(description="🖼️ Generate images with Pollinations AI")
    parser.add_argument("prompt", nargs="?", help="Text prompt for image generation")
    parser.add_argument("-f", "--file", default="prompt.txt", help="Read prompt from file (default: prompt.txt)")
    parser.add_argument("-o", "--output", help="Output filename")
    parser.add_argument("-w", "--width", type=int, default=1024, help="Image width")
    parser.add_argument("--height", type=int, default=1024, help="Image height")
    parser.add_argument("-s", "--seed", type=int, help="Seed for reproducible results")
    parser.add_argument("-m", "--model", default="flux", help="Model to use")
    parser.add_argument("--transparent", action="store_true", help="Generate transparent background")

    args = parser.parse_args()

    generator = ImageGenerator()

    # build params from args
    params = {}
    if args.width != 1024:
        params["width"] = args.width
    if args.height != 1024:
        params["height"] = args.height
    if args.seed:
        params["seed"] = args.seed
    if args.model != "flux":
        params["model"] = args.model
    if args.transparent:
        params["transparent"] = "true"

    if args.prompt:
        # use command line prompt
        generator.generate_image(args.prompt, args.output, **params)
    else:
        # read from file
        generator.generate_from_file(args.file)

if __name__ == "__main__":
    main()
